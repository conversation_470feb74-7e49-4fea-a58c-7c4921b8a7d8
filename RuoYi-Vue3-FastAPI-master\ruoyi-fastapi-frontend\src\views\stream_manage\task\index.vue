<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 120px;">
          <el-option label="停止" value="0" />
          <el-option label="运行中" value="1" />
          <el-option label="暂停" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="VideoPlay"
          :disabled="multiple"
          @click="handleBatchStart"
          v-hasPermi="['stream_manage:task:operate']"
        >批量启动</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="VideoPause"
          :disabled="multiple"
          @click="handleBatchStop"
          v-hasPermi="['stream_manage:task:operate']"
        >批量停止</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['stream_manage:task:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="视频流" align="center" prop="streamName" />
      <el-table-column label="算法配置" align="center" width="150">
        <template #default="scope">
          <div v-if="scope.row.hasAlgorithmConfig">
            <el-tag type="success" size="small">{{ scope.row.algorithmName || '已配置' }}</el-tag>
          </div>
          <el-tag v-else type="danger">未配置</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '0'" type="info">{{ scope.row.statusText }}</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="success">{{ scope.row.statusText }}</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="warning">{{ scope.row.statusText }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="运行次数" align="center" prop="runCount" width="100" />
      <el-table-column label="告警次数" align="center" prop="alertCount" width="100" />
      <el-table-column label="最后运行" align="center" prop="lastRunTime" width="180">
        <template #default="scope">
          <span v-if="scope.row.lastRunTime">{{ scope.row.lastRunTime }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.status === '0'"
            link
            type="success"
            icon="VideoPlay"
            @click="handleStart(scope.row)"
            v-hasPermi="['stream_manage:task:operate']"
            size="small"
          >
            启动
          </el-button>
          <el-button
            v-if="scope.row.status === '1'"
            link
            type="warning"
            icon="VideoPause"
            @click="handleStop(scope.row)"
            v-hasPermi="['stream_manage:task:operate']"
            size="small"
          >
            停止
          </el-button>




          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['stream_manage:task:remove']"
            size="small"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />





  </div>
</template>

<script setup name="Task">
import { listTask, delTask, startTask, stopTask, pauseTask, batchStartTasks, batchStopTasks, startTaskUnified, stopTaskUnified } from "@/api/stream_manage/task";

const { proxy } = getCurrentInstance();

const taskList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const multipleSelection = ref([]);





const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: null,
    status: null,
  }
});

const { queryParams } = toRefs(data);

/** 查询任务列表 */
function getList() {
  loading.value = true;
  listTask(queryParams.value).then(response => {
    taskList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  multipleSelection.value = selection;
  ids.value = selection.map(item => item.taskId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}



/** 删除按钮操作 */
function handleDelete(row) {
  const taskIds = row.taskId || ids.value;
  proxy.$modal.confirm('是否确认删除任务编号为"' + taskIds + '"的数据项？').then(function() {
    return delTask(taskIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 启动任务 */
function handleStart(row) {
  proxy.$modal.confirm('是否确认启动任务 "' + row.taskName + '"？').then(function() {
    return startTaskUnified(row.taskId);
  }).then((response) => {
    console.log('启动任务响应:', response);

    // 检查响应格式
    if (response && response.code === 200) {
      const data = response.data || {};
      const successCount = data.success_count || 0;
      const failedCount = data.failed_count || 0;

      if (successCount > 0) {
        proxy.$modal.msgSuccess("任务启动成功");
      } else if (failedCount > 0) {
        const failedTasks = data.failed_tasks || [];
        const errorMsg = failedTasks.length > 0 ?
          failedTasks.map(t => t.error).join(', ') :
          "任务启动失败";

        // 检查是否是超时或连接错误
        if (errorMsg.includes('超时') || errorMsg.includes('timeout') || errorMsg.includes('连接')) {
          proxy.$modal.confirm(
            `${errorMsg}\n\n建议操作：\n1. 检查视频流地址是否正确\n2. 检查网络连接是否正常\n3. 确认摄像头设备是否在线\n\n是否重新运行任务？`,
            '视频流连接问题',
            {
              confirmButtonText: '重新运行',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            // 用户选择重新运行，再次尝试启动任务
            handleStart(row);
          }).catch(() => {
            // 用户取消，不做任何操作
            console.log('用户取消重新运行任务');
          });
        } else {
          proxy.$modal.msgError(errorMsg);
        }
      } else {
        proxy.$modal.msgError("任务启动失败: 未知错误");
      }
    } else if (response && response.success_count !== undefined) {
      // 直接返回数据格式
      if (response.success_count > 0) {
        proxy.$modal.msgSuccess("任务启动成功");
      } else {
        proxy.$modal.msgError("任务启动失败: " + (response.failed_tasks[0]?.error || "未知错误"));
      }
    } else {
      proxy.$modal.msgError(response?.msg || "任务启动失败");
    }

    getList();
  }).catch((error) => {
    // 检查是否是用户取消操作
    if (error === 'cancel' || error === 'close' || error.message === 'cancel') {
      // 用户取消，不显示错误消息
      console.log('用户取消启动任务');
      return;
    }

    console.error('启动任务异常:', error);
    proxy.$modal.msgError("任务启动异常: " + (error.message || error));
  });
}

/** 停止任务 */
function handleStop(row) {
  proxy.$modal.confirm('是否确认停止任务 "' + row.taskName + '"？').then(function() {
    return stopTaskUnified(row.taskId);
  }).then((response) => {
    console.log('停止任务响应:', response);

    // 检查响应格式
    if (response && response.code === 200) {
      const data = response.data || {};
      const successCount = data.success_count || 0;

      // 停止任务：只要没有异常就算成功
      proxy.$modal.msgSuccess("任务停止成功");
    } else if (response && response.success_count !== undefined) {
      // 直接返回数据格式
      proxy.$modal.msgSuccess("任务停止成功");
    } else {
      proxy.$modal.msgError(response?.msg || "任务停止失败");
    }

    getList();
  }).catch((error) => {
    // 检查是否是用户取消操作
    if (error === 'cancel' || error === 'close' || error.message === 'cancel') {
      // 用户取消，不显示错误消息
      console.log('用户取消停止任务');
      return;
    }

    console.error('停止任务异常:', error);
    proxy.$modal.msgError("任务停止异常: " + (error.message || error));
  });
}

/** 暂停任务 */
function handlePause(row) {
  pauseTask(row.taskId).then(() => {
    proxy.$modal.msgSuccess("任务暂停成功");
    getList();
  });
}

/** 批量启动 */
function handleBatchStart() {
  const ids = multipleSelection.value.map(item => item.taskId);
  if (ids.length === 0) {
    proxy.$modal.msgWarning("请选择要启动的任务");
    return;
  }

  proxy.$modal.confirm('是否确认批量启动选中的任务？').then(function() {
    return batchStartTasks(ids);
  }).then((response) => {
    console.log('批量启动响应:', response);

    if (response && response.code === 200) {
      const data = response.data || {};
      const successCount = data.success_count || 0;
      const failedCount = data.failed_count || 0;
      const totalCount = data.total_count || ids.length;

      if (successCount === totalCount) {
        proxy.$modal.msgSuccess(`批量启动成功，共启动 ${successCount} 个任务`);
      } else if (successCount > 0) {
        proxy.$modal.msgWarning(`部分成功：启动 ${successCount} 个任务，${failedCount} 个失败`);
      } else {
        const failedTasks = data.failed_tasks || [];
        const errorMsg = failedTasks.length > 0 ?
          failedTasks.map(t => t.error).join(', ') :
          "批量启动失败";
        proxy.$modal.msgError(errorMsg);
      }
    } else {
      proxy.$modal.msgError(response?.msg || "批量启动失败");
    }

    getList();
  }).catch((error) => {
    // 检查是否是用户取消操作
    if (error === 'cancel' || error === 'close' || error.message === 'cancel') {
      // 用户取消，不显示错误消息
      console.log('用户取消批量启动任务');
      return;
    }

    console.error('批量启动异常:', error);
    proxy.$modal.msgError("批量启动异常: " + (error.message || error));
  });
}

/** 批量停止 */
function handleBatchStop() {
  const ids = multipleSelection.value.map(item => item.taskId);
  if (ids.length === 0) {
    proxy.$modal.msgWarning("请选择要停止的任务");
    return;
  }

  proxy.$modal.confirm('是否确认批量停止选中的任务？').then(function() {
    return batchStopTasks(ids);
  }).then((response) => {
    console.log('批量停止响应:', response);

    if (response && response.code === 200) {
      const data = response.data || {};
      const successCount = data.success_count || 0;

      // 停止任务：只要没有异常就算成功
      proxy.$modal.msgSuccess(`批量停止成功，共停止 ${successCount} 个任务`);
    } else {
      proxy.$modal.msgError(response?.msg || "批量停止失败");
    }

    getList();
  }).catch((error) => {
    // 检查是否是用户取消操作
    if (error === 'cancel' || error === 'close' || error.message === 'cancel') {
      // 用户取消，不显示错误消息
      console.log('用户取消批量停止任务');
      return;
    }

    console.error('批量停止异常:', error);
    proxy.$modal.msgError("批量停止异常: " + (error.message || error));
  });
}





getList();
</script>
