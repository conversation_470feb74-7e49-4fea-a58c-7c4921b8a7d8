2025-07-28 09:27:28.079 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 09:27:28.080 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 09:27:28.105 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 09:27:28.105 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 09:27:28.106 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 09:27:28.137 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 09:27:28.146 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 09:27:28.147 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 09:27:42.523 | 2c5096a426e444a496396de3ddee9c57 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 947: 实体对象 = True
2025-07-28 09:27:42.523 | 2c5096a426e444a496396de3ddee9c57 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_090449_886.jpg
2025-07-28 09:27:42.524 | 2c5096a426e444a496396de3ddee9c57 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_090449_886.jpg
2025-07-28 09:27:42.525 | 2c5096a426e444a496396de3ddee9c57 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 947
2025-07-28 09:27:42.529 | 2c5096a426e444a496396de3ddee9c57 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:116 - 成功删除1条记录，删除1个截图文件
2025-07-28 09:27:42.569 | 45092cefc7fa4500a9eec50f3e6268a8 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:27:44.978 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:27:44.978 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:27:44.978 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:27:44.978 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:27:44.979 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:27:44.979 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:27:44.979 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:27:44.979 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:27:44.980 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:27:44.980 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:27:44.984 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:27:44.985 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:27:44.985 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:27:44.985 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:27:44.986 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:27:44.987 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:27:44.987 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:27:44.988 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:27:44.988 | c18c1e89c494449d8e05aea5a2be9372 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:27:46.677 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:27:46.680 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:470 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:27:46.680 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:477 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:27:46.680 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:484 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:27:46.680 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:491 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:27:46.680 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:497 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:27:46.681 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:27:46.681 | e3dddb2523dd443d8cdee79a40a403a0 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:592 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:27:46.681 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:616 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 09:27:46.682 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:631 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:27:46.682 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:636 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 09:27:46.682 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:637 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:27:46.683 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:641 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:27:47.863 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:27:47.863 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:47.863 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:27:47.863 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:27:47.863 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:27:47.864 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:674 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:27:50.007 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:686 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:27:50.008 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:687 -    - 设备: cuda
2025-07-28 09:27:50.008 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:688 -    - 图像尺寸: 640
2025-07-28 09:27:50.008 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 -    - 置信度阈值: 0.25
2025-07-28 09:27:50.008 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:690 -    - NMS阈值: 0.45
2025-07-28 09:27:50.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:703 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:27:50.025 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3216 - 任务 12 的监控流已启动
2025-07-28 09:27:50.027 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:27:50.027 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:751 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:27:50.028 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:781 - 成功预导入YOLOv5 utils模块
2025-07-28 09:27:50.028 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:787 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:27:50.028 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: zql_detect
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: model
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 成功导入智驱力模型
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:27:50.029 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:27:50.030 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:819 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:27:50.143 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:832 - 智驱力模型初始化成功
2025-07-28 09:27:50.143 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:833 -    - 设备: cuda
2025-07-28 09:27:50.143 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:834 -    - 图像尺寸: 640
2025-07-28 09:27:50.143 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:835 -    - 置信度阈值: 0.25
2025-07-28 09:27:50.144 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:836 -    - NMS阈值: 0.45
2025-07-28 09:27:50.144 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:849 - 智驱力后处理器初始化成功
2025-07-28 09:27:54.457 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:54.458 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 19
2025-07-28 09:27:54.458 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 400, 905, 513]}
2025-07-28 09:27:54.458 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 400, 905, 513]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [119, 504, 327, 666]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [596, 236, 658, 297]}]
2025-07-28 09:27:54.458 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:54.458 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:54.459 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:54.459 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:54.459 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:54.459 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 从user_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:27:54.459 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:982 - 从algorithm_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:27:54.460 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1013 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:27:54.462 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1018 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:27:54.462 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1020 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:27:54.462 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1022 - ⚠️ 任务12 - hit: False
2025-07-28 09:27:54.462 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1023 - ⚠️ 任务12 - message: 检测到 16 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:27:54.462 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1026 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:27:54.463 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1041 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 400, 905, 513], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:27:54.463 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1043 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:27:54.463 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1071 - 后处理结果: {'hit': False, 'message': '检测到 16 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 400, 905, 513], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [119, 504, 327, 666], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [596, 236, 658, 297], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [397, 448, 537, 578], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [267, 331, 385, 441], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [144, 310, 261, 414], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [485, 205, 544, 260], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [359, 233, 430, 289], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 525, 72, 656], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [606, 326, 699, 413], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [258, 573, 557, 672], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 333, 540, 421], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [245, 227, 320, 294], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [553, 152, 599, 193], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [622, 591, 808, 673], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [427, 154, 468, 193], 'track_id': 15, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:27:54.462452', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753666074.4624581}
2025-07-28 09:27:54.468 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:27:54.468 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:27:54.478 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:27:54.478 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:27:54.478 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:27:54.479 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:27:54.479 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:27:54.479 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:27:54.479 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:27:54.480 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:27:54.480 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:27:54.484 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:27:54.484 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:27:54.484 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:27:54.484 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:27:54.486 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:27:54.488 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:27:54.488 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:27:54.488 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:27:54.488 | 9b933369122d49d49a8f824dd3b1e01d | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:27:54.490 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:27:54.490 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:27:54.491 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:27:54.491 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:27:54.491 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:27:54.491 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:27:54.492 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:27:54.492 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:27:54.492 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:27:54.492 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:27:54.495 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:27:54.495 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3273 - 客户端 ebe0a667-8fe1-4cbe-b804-e9174f7574a8 已连接到任务 12 的监控流
2025-07-28 09:27:54.495 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 ebe0a667-8fe1-4cbe-b804-e9174f7574a8 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:27:55.514 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:27:55.514 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:27:55.514 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:27:55.514 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 19
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [756, 362, 881, 485]}
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [756, 362, 881, 485]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [149, 477, 333, 623]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [617, 561, 803, 672]}]
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:27:55.534 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:55.535 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:55.535 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:55.535 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:55.535 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:27:55.535 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:27:55.536 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:27:55.537 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:27:55.538 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:27:55.538 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:27:55.538 | a23797dd8f7344b89a2fcd2aeace9aba | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:27:56.586 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [751, 365, 871, 470]}
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [751, 365, 871, 470]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [615, 542, 795, 671]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [162, 459, 343, 612]}]
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:27:56.587 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:56.588 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:56.588 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:56.588 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:56.589 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:27:56.589 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [182, 448, 351, 584]}
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [182, 448, 351, 584]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 490, 107, 639]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [744, 354, 859, 453]}]
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:57.638 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:27:57.639 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:57.639 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:57.639 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:57.639 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:57.639 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:27:57.640 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:27:58.687 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:58.688 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:27:58.688 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [604, 502, 779, 663]}
2025-07-28 09:27:58.688 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [604, 502, 779, 663]}, {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [736, 345, 848, 440]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 481, 113, 622]}]
2025-07-28 09:27:58.688 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:58.689 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:27:58.690 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:27:59.738 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:27:59.738 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:27:59.739 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [603, 483, 767, 638]}
2025-07-28 09:27:59.739 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [603, 483, 767, 638]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [735, 335, 840, 430]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 472, 123, 606]}]
2025-07-28 09:27:59.739 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:27:59.739 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:27:59.739 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:27:59.740 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:27:59.740 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:27:59.740 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:27:59.740 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:27:59.740 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:00.798 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:00.799 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:28:00.799 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [601, 467, 756, 611]}
2025-07-28 09:28:00.799 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [601, 467, 756, 611]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [733, 325, 830, 415]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 463, 132, 591]}]
2025-07-28 09:28:00.799 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:00.799 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:00.800 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:00.800 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:00.800 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:00.800 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:00.800 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:00.801 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:01.853 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:01.853 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:28:01.853 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [599, 449, 749, 588]}
2025-07-28 09:28:01.854 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [599, 449, 749, 588]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 450, 141, 578]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [728, 313, 828, 406]}]
2025-07-28 09:28:01.854 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:01.854 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:01.854 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:01.855 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:01.855 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:01.855 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:01.855 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:01.856 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [597, 434, 738, 565]}
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [597, 434, 738, 565]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 439, 149, 567]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [725, 301, 823, 395]}]
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:02.909 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:02.910 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:02.910 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:02.910 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:02.910 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:02.910 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:02.911 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 19
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [593, 419, 730, 544]}
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [593, 419, 730, 544]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 432, 153, 553]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [253, 360, 381, 470]}]
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:03.957 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:03.958 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:03.958 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:03.958 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:03.958 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:03.959 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 18
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 421, 162, 538]}
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 421, 162, 538]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [594, 405, 721, 524]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [62, 531, 285, 668]}]
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:05.010 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:05.011 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:05.011 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:05.011 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:05.011 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:05.011 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:05.012 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:06.059 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:06.059 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 18
2025-07-28 09:28:06.060 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [591, 388, 714, 504]}
2025-07-28 09:28:06.060 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [591, 388, 714, 504]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [3, 407, 170, 529]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [82, 505, 298, 667]}]
2025-07-28 09:28:06.060 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:06.060 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:06.061 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:06.070 | f73f44fc61e74d86ad694d5220236bb0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:28:07.087 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 ebe0a667-8fe1-4cbe-b804-e9174f7574a8 主动断开连接
2025-07-28 09:28:07.087 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:3286 - 客户端 ebe0a667-8fe1-4cbe-b804-e9174f7574a8 已断开任务 12 的监控流
2025-07-28 09:28:07.088 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:3290 - 任务 12 没有监控客户端，保持监控流运行
2025-07-28 09:28:07.088 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 ebe0a667-8fe1-4cbe-b804-e9174f7574a8 连接已清理
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 19
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 371, 707, 487]}
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 371, 707, 487]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 187, 638, 225]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [728, 283, 803, 360]}]
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:07.106 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:07.107 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:07.107 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:07.107 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:07.107 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:07.107 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:07.108 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:07.116 | b2139d07c7194718a9536285d786f87a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:28:07.232 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:07.233 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:28:07.233 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [588, 359, 698, 460]}
2025-07-28 09:28:07.233 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [588, 359, 698, 460]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [32, 392, 200, 499]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [642, 542, 845, 673]}]
2025-07-28 09:28:07.233 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:07.234 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:07.234 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:07.234 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:07.235 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:07.235 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:07.235 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:07.235 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:07.250 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:28:07.250 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:28:07.250 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:28:07.251 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:28:07.254 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:28:07.255 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:28:07.255 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:28:07.255 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:28:07.256 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:28:07.257 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:28:07.258 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:28:07.258 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:28:07.258 | 241605e1a02a4ebc89cdc433009c9a17 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:28:07.264 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:28:07.264 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3273 - 客户端 a2d7147e-8c24-4f1a-ad89-7da60e0dbc3e 已连接到任务 12 的监控流
2025-07-28 09:28:07.265 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 a2d7147e-8c24-4f1a-ad89-7da60e0dbc3e 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:28:08.298 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:08.298 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:28:08.298 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [638, 517, 813, 667]}
2025-07-28 09:28:08.298 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [638, 517, 813, 667]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [589, 351, 692, 442]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [732, 273, 815, 346]}]
2025-07-28 09:28:08.299 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:08.299 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:08.299 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:08.300 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:08.300 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:08.300 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:08.300 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:08.301 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:09.455 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:09.455 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:28:09.456 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [634, 496, 802, 654]}
2025-07-28 09:28:09.456 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [634, 496, 802, 654]}, {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [588, 337, 687, 429]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [734, 267, 819, 346]}]
2025-07-28 09:28:09.456 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:09.456 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:09.456 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:09.457 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:09.457 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:09.457 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:09.457 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:09.457 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.93, 'name': 'car', 'ch_name': 'car', 'xyxy': [629, 475, 789, 619]}
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.93, 'name': 'car', 'ch_name': 'car', 'xyxy': [629, 475, 789, 619]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [738, 263, 826, 341]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [587, 327, 682, 415]}]
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:10.477 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:10.478 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:10.478 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:10.478 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:10.479 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:10.479 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:11.540 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:11.540 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:28:11.540 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [624, 452, 776, 589]}
2025-07-28 09:28:11.540 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [624, 452, 776, 589]}, {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [72, 362, 223, 460]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [583, 316, 680, 405]}]
2025-07-28 09:28:11.541 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:11.541 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:11.541 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:11.541 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:11.541 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:11.542 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:11.542 | e3dddb2523dd443d8cdee79a40a403a0 | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d1349be490> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:11.542 | e3dddb2523dd443d8cdee79a40a403a0 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:11.555 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:279 - 开始停止任务: 12
2025-07-28 09:28:12.571 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:295 - 取消异步任务: 12
2025-07-28 09:28:12.571 | e3dddb2523dd443d8cdee79a40a403a0 | INFO     | module_stream.service.task_execution_service:detection_loop:1159 - 检测任务被取消: 12
2025-07-28 09:28:12.595 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:303 - 已从运行任务列表移除: 12
2025-07-28 09:28:12.595 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:312 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:28:12.595 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3240 - 任务 12 的监控流已停止
2025-07-28 09:28:12.595 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:317 - 监控流停止成功: 12
2025-07-28 09:28:12.596 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:28:12.596 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:326 - 任务缓存清理成功: 12
2025-07-28 09:28:12.605 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:335 - 任务状态更新成功: 12
2025-07-28 09:28:12.606 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.service.task_execution_service:stop_task:347 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:28:12.606 | 61f40fac33004bd482a1d5444cb5b33d | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:28:12.650 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 a2d7147e-8c24-4f1a-ad89-7da60e0dbc3e 主动断开连接
2025-07-28 09:28:12.650 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 a2d7147e-8c24-4f1a-ad89-7da60e0dbc3e 连接已清理
2025-07-28 09:28:37.183 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:28:37.183 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:28:37.183 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:28:37.184 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:28:37.184 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:28:37.184 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:28:37.184 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:28:37.184 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:28:37.188 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:28:37.188 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:28:37.193 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:28:37.193 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:28:37.193 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:28:37.193 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:28:37.195 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:28:37.196 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:28:37.196 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:28:37.197 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:28:37.197 | 87b6faef7a524093894a03ee5c786b1f | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:28:39.696 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:28:39.698 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:470 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:28:39.698 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:477 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:28:39.698 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:484 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:28:39.699 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:491 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:28:39.699 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:497 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:28:39.699 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:28:39.699 | 23488e2e6bee486db84d170570617d2b | WARNING  | module_stream.service.task_execution_service:_validate_required_config:592 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:28:39.700 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:631 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:28:39.700 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:636 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\model', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master']
2025-07-28 09:28:39.701 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:637 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:28:39.701 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:641 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:28:39.701 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:658 - 验证模型初始化 - 重新加载模块: zql_detect
2025-07-28 09:28:39.702 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:658 - 验证模型初始化 - 重新加载模块: model
2025-07-28 09:28:39.702 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:28:39.703 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:39.703 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:28:39.703 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:28:39.703 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:28:39.703 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:674 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:28:39.846 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:686 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:28:39.846 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:687 -    - 设备: cuda
2025-07-28 09:28:39.846 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:688 -    - 图像尺寸: 640
2025-07-28 09:28:39.846 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 -    - 置信度阈值: 0.25
2025-07-28 09:28:39.846 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:690 -    - NMS阈值: 0.45
2025-07-28 09:28:39.847 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:703 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:28:39.861 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3216 - 任务 12 的监控流已启动
2025-07-28 09:28:39.862 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:28:39.863 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:751 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:28:39.863 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:781 - 成功预导入YOLOv5 utils模块
2025-07-28 09:28:39.863 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:787 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:28:39.864 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: zql_detect
2025-07-28 09:28:39.864 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: model
2025-07-28 09:28:39.865 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 成功导入智驱力模型
2025-07-28 09:28:39.865 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:39.865 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:28:39.865 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:28:39.865 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:28:39.866 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:819 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:28:40.001 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:832 - 智驱力模型初始化成功
2025-07-28 09:28:40.001 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:833 -    - 设备: cuda
2025-07-28 09:28:40.001 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:834 -    - 图像尺寸: 640
2025-07-28 09:28:40.001 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:835 -    - 置信度阈值: 0.25
2025-07-28 09:28:40.002 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:836 -    - NMS阈值: 0.45
2025-07-28 09:28:40.002 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:849 - 智驱力后处理器初始化成功
2025-07-28 09:28:59.048 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:59.049 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:28:59.049 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:28:59.049 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:28:59.050 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:59.050 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:59.050 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:59.050 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:59.050 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:59.051 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 从user_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:28:59.051 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:982 - 从algorithm_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:28:59.051 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1013 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:28:59.053 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1018 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:28:59.053 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1020 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1022 - ⚠️ 任务12 - hit: False
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1023 - ⚠️ 任务12 - message: 检测到 2 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1026 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1041 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1043 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:28:59.054 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1071 - 后处理结果: {'hit': False, 'message': '检测到 2 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188], 'track_id': 1, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:28:59.053680', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753666139.0536864}
2025-07-28 09:28:59.064 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:28:59.064 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:28:59.064 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:28:59.064 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:28:59.065 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:28:59.065 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:28:59.065 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:28:59.065 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:28:59.065 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:28:59.068 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:28:59.068 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:28:59.069 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:28:59.069 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:28:59.069 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:28:59.069 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:28:59.071 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:28:59.072 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:28:59.072 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:28:59.072 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:28:59.072 | ada4c8d5dee7473aaf8a637a311f1b2b | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:28:59.085 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:28:59.085 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:28:59.085 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:28:59.086 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:28:59.086 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:28:59.086 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:28:59.086 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:28:59.087 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:28:59.087 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:28:59.087 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:28:59.090 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:28:59.090 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:28:59.091 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:28:59.091 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:28:59.093 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:28:59.094 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:28:59.094 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:28:59.094 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:28:59.094 | 3c5c4161c4354fa8aa1c2915c6fd7c6a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:28:59.296 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:59.296 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:28:59.296 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:28:59.296 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:28:59.296 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:59.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:59.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:59.297 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:59.297 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:59.297 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:59.298 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:59.298 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:59.544 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:59.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:28:59.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:28:59.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:28:59.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:59.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:59.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:59.666 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:59.666 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:59.667 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:59.668 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:59.668 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:59.668 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:59.668 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:28:59.795 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:28:59.796 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:28:59.796 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:28:59.796 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:28:59.796 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:28:59.796 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:28:59.797 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.040 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.040 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:00.040 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.041 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.042 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.042 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.042 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.173 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.173 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.173 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.173 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.173 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.297 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.298 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.298 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.298 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.298 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.299 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.299 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.547 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.547 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:29:00.547 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.547 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:00.548 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.548 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.548 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.548 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.548 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.549 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.549 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.549 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.672 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.672 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.672 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.672 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.672 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.673 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:00.796 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:00.796 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:29:00.797 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:00.797 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:00.797 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:00.797 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:00.797 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:00.798 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:00.798 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:00.798 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:00.798 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:00.798 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.045 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.045 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:29:01.045 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:01.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:01.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.047 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.171 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.171 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:29:01.171 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:01.171 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.172 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.173 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.173 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.298 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.298 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 5
2025-07-28 09:29:01.298 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:01.299 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [1229, 190, 1280, 214]}]
2025-07-28 09:29:01.299 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.299 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.299 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.300 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.300 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.300 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.300 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.300 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.542 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.542 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:29:01.542 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:29:01.542 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [1215, 187, 1278, 213]}]
2025-07-28 09:29:01.542 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.543 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.544 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.678 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.679 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.680 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.680 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.680 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.680 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:01.803 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:01.803 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 7
2025-07-28 09:29:01.803 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:29:01.803 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:01.803 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:01.804 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:01.805 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.040 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}]
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.041 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.042 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.042 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.042 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.159 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.159 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.160 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.161 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.161 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.161 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.292 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.292 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:29:02.292 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:29:02.292 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.293 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.294 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.294 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.544 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.544 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.545 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.546 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.546 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:02.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:29:02.679 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:29:02.680 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.680 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.680 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.680 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.680 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.681 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.681 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.681 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:02.805 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:02.806 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:02.806 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:02.806 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:02.806 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:02.807 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:03.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:03.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:29:03.046 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:03.047 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:03.048 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:03.048 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.178 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:03.179 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:03.180 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:03.180 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:03.180 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:03.180 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.303 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:03.303 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [319, 133, 355, 162]}]
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:03.304 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:03.305 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:03.305 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:03.305 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:03.541 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:03.542 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:03.542 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:03.542 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:03.542 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:03.543 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.563 | 5db97312e7d2475ba4655365c76e3df7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:29:03.579 | 645aa6f93c254742bce989d7e952d3ee | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:29:03.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:03.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:03.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:03.671 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:03.672 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:03.672 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:03.672 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:03.672 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:03.672 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:03.673 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:03.673 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:03.673 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:03.695 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:29:03.695 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:29:03.695 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:29:03.695 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:29:03.696 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:29:03.696 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:29:03.696 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:29:03.696 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:29:03.696 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:29:03.700 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:29:03.701 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:29:03.701 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:29:03.701 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:29:03.702 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:29:03.703 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:29:03.703 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:29:03.703 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:29:03.704 | 76431a8481a142b0b22e2012eb889554 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:29:03.708 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:29:03.709 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3273 - 客户端 f21eaa78-781d-4330-8ff5-329e3d5ee934 已连接到任务 12 的监控流
2025-07-28 09:29:03.709 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 f21eaa78-781d-4330-8ff5-329e3d5ee934 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:29:04.742 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:04.743 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:04.744 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:04.744 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:04.744 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:05.786 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:05.786 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:05.787 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:05.787 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:05.787 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:05.788 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:05.789 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:06.847 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:06.847 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:06.848 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:06.849 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:06.849 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:06.849 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:07.900 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:07.900 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:07.900 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:07.901 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:07.902 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:07.902 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:07.902 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:08.952 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:08.952 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:08.952 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:08.952 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:08.952 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:08.953 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:08.953 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:08.953 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:08.953 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:08.953 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:08.954 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:08.954 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:10.011 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:10.011 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:10.011 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 161]}]
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:10.012 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:10.013 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:10.013 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:10.013 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:11.061 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:11.061 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:11.062 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:11.063 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:11.063 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:11.063 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:11.063 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:12.112 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:12.113 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:12.114 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:12.114 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:12.114 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:13.165 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:888 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:29:13.165 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:890 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:29:13.165 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:892 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:29:13.165 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:893 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:29:13.166 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:29:13.166 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1215 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:29:13.166 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1220 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:29:13.166 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:957 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:29:13.166 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:958 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:29:13.167 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:960 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:29:13.167 | 23488e2e6bee486db84d170570617d2b | ERROR    | module_stream.service.task_execution_service:detection_loop:1074 - 检测或后处理失败: Instance <SurveillanceTask at 0x1d358321450> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:29:13.167 | 23488e2e6bee486db84d170570617d2b | DEBUG    | module_stream.service.task_execution_service:detection_loop:1090 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:29:13.178 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:279 - 开始停止任务: 12
2025-07-28 09:29:13.179 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:295 - 取消异步任务: 12
2025-07-28 09:29:13.179 | 23488e2e6bee486db84d170570617d2b | INFO     | module_stream.service.task_execution_service:detection_loop:1159 - 检测任务被取消: 12
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:303 - 已从运行任务列表移除: 12
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:312 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3240 - 任务 12 的监控流已停止
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:317 - 监控流停止成功: 12
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:29:13.202 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:326 - 任务缓存清理成功: 12
2025-07-28 09:29:13.208 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:335 - 任务状态更新成功: 12
2025-07-28 09:29:13.208 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.service.task_execution_service:stop_task:347 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:29:13.209 | 31a1ba31b0504bcdba5c8d6d7fe2e732 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:29:13.240 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 f21eaa78-781d-4330-8ff5-329e3d5ee934 主动断开连接
2025-07-28 09:29:13.240 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 f21eaa78-781d-4330-8ff5-329e3d5ee934 连接已清理
2025-07-28 09:31:41.261 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 09:31:41.261 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
