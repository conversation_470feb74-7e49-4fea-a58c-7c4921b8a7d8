2025-07-28 09:40:12.194 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 09:40:12.194 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 09:40:12.219 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 09:40:12.220 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 09:40:12.221 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 09:40:12.253 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 09:40:12.264 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 09:40:12.264 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 09:42:20.412 | 54e63e4a02d94a34becb946628741d02 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1df33839-cfc6-4fd4-888a-0292237158ba的会话获取图片验证码成功
2025-07-28 09:42:25.980 | 29073546af4a4fafb5a43863a302f96d | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-28 09:42:26.003 | e46f2f681ef04fa1821a0bb0c80402c7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:42:26.016 | 0970db489ae84675a1f70dd4835b188b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:42:29.427 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:42:29.428 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:42:29.428 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:42:29.428 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:29.428 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:42:29.429 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:42:29.429 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:42:29.429 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:29.430 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:42:29.430 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:42:29.436 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:42:29.437 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:42:29.437 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:42:29.437 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:42:29.438 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:42:29.440 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:42:29.440 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:42:29.440 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:42:29.440 | 06e140c133fc471b9341aaec91662bb6 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:42:36.008 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:42:36.009 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:42:36.009 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:42:36.009 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:42:36.009 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:42:36.009 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:42:36.010 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:42:36.010 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:42:36.010 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:42:36.013 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:42:36.014 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:42:36.014 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:42:36.014 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:42:36.015 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:42:36.016 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:42:36.017 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:42:36.017 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:42:36.017 | bfd861c176494ba3a8b3ed40c6efe75b | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:42:36.022 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:42:36.022 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 add96ba2-4fc0-494c-8554-d749bce7e3d3 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:42:38.169 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 add96ba2-4fc0-494c-8554-d749bce7e3d3 主动断开连接
2025-07-28 09:42:38.169 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 add96ba2-4fc0-494c-8554-d749bce7e3d3 连接已清理
2025-07-28 09:42:38.715 | 28e65444de3e4d299fec0dedf0b5ee2b | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:42:40.764 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:42:40.764 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:42:40.764 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:42:40.764 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:40.764 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:42:40.765 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:42:40.765 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:42:40.765 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:40.765 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:42:40.766 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:42:40.770 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:42:40.770 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:42:40.770 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:42:40.770 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:42:40.771 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:42:40.772 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:42:40.772 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:42:40.772 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:42:40.773 | 362ed923f49a40bb99bf476006f8147e | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:42:42.885 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:279 - 开始停止任务: 12
2025-07-28 09:42:42.887 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:305 - 任务 12 不在运行任务列表中
2025-07-28 09:42:42.887 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:312 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:42:42.887 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:317 - 监控流停止成功: 12
2025-07-28 09:42:42.888 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:42:42.888 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:326 - 任务缓存清理成功: 12
2025-07-28 09:42:42.894 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:335 - 任务状态更新成功: 12
2025-07-28 09:42:42.894 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.service.task_execution_service:stop_task:347 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:42:42.894 | dea54fdd4027470eb4cf00b93325da75 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:42:42.910 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:42:42.910 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:42:42.911 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:42:42.912 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:42:42.912 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:42:42.915 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:42:42.916 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:42:42.916 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:42:42.916 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:42:42.917 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:42:42.918 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:42:42.918 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:42:42.919 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:42:42.919 | 98cb6acd43434beeab63d3b75672916a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:43:09.569 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:43:09.571 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:470 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:43:09.571 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:477 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:43:09.572 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:484 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:43:09.572 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:491 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:43:09.572 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:497 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:43:09.572 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:43:09.572 | e4aacafd87f442f3896db5eb250790a6 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:592 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:43:09.573 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:616 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 09:43:09.574 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:631 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:43:09.574 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:636 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 09:43:09.574 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:637 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:43:09.574 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:641 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:43:10.958 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:43:10.958 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:10.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:43:10.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:43:10.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:43:10.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:674 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:43:13.275 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:686 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:43:13.275 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:687 -    - 设备: cuda
2025-07-28 09:43:13.276 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:688 -    - 图像尺寸: 640
2025-07-28 09:43:13.276 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 -    - 置信度阈值: 0.25
2025-07-28 09:43:13.276 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:690 -    - NMS阈值: 0.45
2025-07-28 09:43:13.277 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:703 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:43:13.293 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3295 - 任务 12 的监控流已启动
2025-07-28 09:43:13.294 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:43:13.295 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:751 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:43:13.295 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:781 - 成功预导入YOLOv5 utils模块
2025-07-28 09:43:13.295 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:787 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:43:13.296 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: zql_detect
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: model
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 成功导入智驱力模型
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:43:13.297 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:819 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:43:13.435 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:832 - 智驱力模型初始化成功
2025-07-28 09:43:13.435 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:833 -    - 设备: cuda
2025-07-28 09:43:13.435 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:834 -    - 图像尺寸: 640
2025-07-28 09:43:13.435 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:835 -    - 置信度阈值: 0.25
2025-07-28 09:43:13.435 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:836 -    - NMS阈值: 0.45
2025-07-28 09:43:13.436 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:849 - 智驱力后处理器初始化成功
2025-07-28 09:43:32.509 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:32.510 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:32.511 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1009 - 从user_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:43:32.511 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1016 - 从algorithm_config获取参数: conf=0.25, nms=0.5, size=640
2025-07-28 09:43:32.511 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1047 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1052 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1054 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1056 - ⚠️ 任务12 - hit: False
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1057 - ⚠️ 任务12 - message: 检测到 2 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1060 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1075 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:43:32.514 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1077 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:43:32.515 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1105 - 后处理结果: {'hit': False, 'message': '检测到 2 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188], 'track_id': 1, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:43:32.514037', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753667012.5140429}
2025-07-28 09:43:32.523 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:43:32.524 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:43:32.528 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:43:32.528 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:43:32.528 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:43:32.528 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:43:32.529 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:43:32.529 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:43:32.529 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:43:32.529 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:43:32.530 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:43:32.535 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:43:32.535 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:43:32.535 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:43:32.536 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:43:32.538 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:43:32.540 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:43:32.540 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:43:32.540 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:43:32.540 | 0d6f209f3970463cb39bef224896916e | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:43:32.544 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:43:32.544 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:43:32.545 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:43:32.545 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:43:32.545 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:43:32.545 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:43:32.546 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:43:32.546 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:43:32.546 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:43:32.546 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:43:32.549 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:43:32.549 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:43:32.550 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:43:32.550 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:43:32.551 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:43:32.552 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:43:32.552 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:43:32.552 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:43:32.552 | 028bfb6d177a4118b766dc56a46210e4 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:43:32.647 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:32.647 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:32.647 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:32.647 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:32.648 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:32.649 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:32.649 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:32.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:32.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:32.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:32.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:32.842 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:32.842 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:32.963 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:32.964 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:32.965 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:32.965 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:32.965 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:33.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.093 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.094 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:33.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.341 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:43:33.341 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.341 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.341 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.341 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.342 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.342 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.342 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.342 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.468 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.469 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.470 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.470 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.470 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.594 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:33.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:33.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.841 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.842 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:33.970 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:33.970 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:33.970 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:33.971 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:33.972 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:33.972 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:33.972 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.099 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.099 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:34.099 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:34.099 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.100 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.101 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.337 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.337 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:34.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:34.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:34.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.463 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.463 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 09:43:34.463 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:34.464 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 09:43:34.464 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.464 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.464 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.464 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.465 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.465 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.465 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.465 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 5
2025-07-28 09:43:34.595 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [1229, 190, 1280, 214]}]
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.596 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.597 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.597 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.839 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [1215, 187, 1278, 213]}]
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.841 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.842 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:34.965 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:34.965 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:34.966 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:34.966 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:43:34.966 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:34.966 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:34.967 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:34.967 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:34.967 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:34.967 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:34.967 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:34.968 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.091 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 7
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.092 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.093 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}]
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.338 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.339 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.339 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.340 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.469 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.470 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.471 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.471 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.471 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.471 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.471 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.597 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.598 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 09:43:35.598 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:43:35.598 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 09:43:35.598 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.599 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.600 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.839 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.839 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.840 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.841 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.841 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.842 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:35.972 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:35.973 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:35.973 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:35.973 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:35.973 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:35.973 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:35.974 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.096 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.096 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.096 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.097 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.098 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.098 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.341 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.342 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.343 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.343 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.343 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.344 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.473 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.474 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.474 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.474 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.474 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.475 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.475 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.602 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.602 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.602 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}
2025-07-28 09:43:36.603 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [319, 133, 355, 162]}]
2025-07-28 09:43:36.603 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.603 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.603 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.603 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.604 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.604 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.604 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.605 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.838 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.838 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.838 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:36.838 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:36.838 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.839 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.839 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.839 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.839 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.839 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.840 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.840 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:36.958 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:36.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:36.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:36.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:36.959 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:36.960 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:36.961 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:37.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:37.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:37.092 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:37.093 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:37.094 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:37.094 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:37.339 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:37.339 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:37.340 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:37.341 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:37.341 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:37.341 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:37.350 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:43:37.350 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:43:37.350 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:43:37.351 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:43:37.351 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:43:37.351 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:43:37.351 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:43:37.351 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:43:37.352 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:43:37.354 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:43:37.354 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:43:37.355 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:43:37.355 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:43:37.356 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:43:37.358 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:43:37.358 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:43:37.358 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:43:37.358 | 591c11d6f2194ba8a216f72ead340c9d | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:43:37.363 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:43:37.364 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3352 - 客户端 e8601af0-0c6a-466c-8bec-6c208a298d27 已连接到任务 12 的监控流
2025-07-28 09:43:37.364 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 e8601af0-0c6a-466c-8bec-6c208a298d27 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:43:38.401 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:38.401 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:38.401 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:38.402 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:38.402 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:38.402 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:38.403 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:38.403 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:38.403 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:38.403 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:38.403 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:38.404 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:39.450 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:39.451 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:39.451 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:39.451 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:39.451 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:39.452 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:40.504 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:40.505 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:40.506 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:40.506 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:40.506 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:40.506 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:40.506 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:41.559 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:41.559 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 161]}]
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:41.560 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:41.561 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:41.561 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:41.561 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:41.561 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:42.613 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:42.614 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:42.614 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:42.614 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:42.614 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:922 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:924 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:926 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:927 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:43:43.675 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1265 - 从config_data获取置信度阈值（最高优先级）: 0.25
2025-07-28 09:43:43.676 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1270 - 最终设置后处理器置信度阈值: 0.25
2025-07-28 09:43:43.676 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:991 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:43:43.676 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:992 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:43:43.676 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:994 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:43:43.676 | e4aacafd87f442f3896db5eb250790a6 | ERROR    | module_stream.service.task_execution_service:detection_loop:1108 - 检测或后处理失败: Instance <SurveillanceTask at 0x263c6acead0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-28 09:43:43.677 | e4aacafd87f442f3896db5eb250790a6 | DEBUG    | module_stream.service.task_execution_service:detection_loop:1124 - 创建默认alert_result，包含配置区域1个
2025-07-28 09:43:43.687 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:279 - 开始停止任务: 12
2025-07-28 09:43:43.689 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:295 - 取消异步任务: 12
2025-07-28 09:43:43.689 | e4aacafd87f442f3896db5eb250790a6 | INFO     | module_stream.service.task_execution_service:detection_loop:1198 - 检测任务被取消: 12
2025-07-28 09:43:44.715 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:303 - 已从运行任务列表移除: 12
2025-07-28 09:43:44.715 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:312 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:43:44.715 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3319 - 任务 12 的监控流已停止
2025-07-28 09:43:44.715 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:317 - 监控流停止成功: 12
2025-07-28 09:43:44.716 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:43:44.716 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:326 - 任务缓存清理成功: 12
2025-07-28 09:43:44.726 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:335 - 任务状态更新成功: 12
2025-07-28 09:43:44.726 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.service.task_execution_service:stop_task:347 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:43:44.726 | 5e82acc506574855a347c08af7d1cc5f | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:43:44.762 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 e8601af0-0c6a-466c-8bec-6c208a298d27 主动断开连接
2025-07-28 09:43:44.762 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 e8601af0-0c6a-466c-8bec-6c208a298d27 连接已清理
2025-07-28 09:45:35.693 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 09:45:35.693 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
