2025-07-28 09:35:54.866 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 09:35:54.866 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 09:35:54.892 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 09:35:54.892 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 09:35:54.894 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 09:35:54.928 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 09:35:54.950 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 09:35:54.950 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 09:36:04.027 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:36:04.028 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:36:04.028 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:36:04.028 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:36:04.028 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:36:04.028 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:36:04.029 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:36:04.029 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:36:04.029 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:36:04.029 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:36:04.036 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:36:04.036 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:36:04.036 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:36:04.037 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:36:04.039 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:36:04.040 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:36:04.040 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:36:04.040 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:36:04.040 | 064ad59ff5274142bbfdc58c4614d4ff | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:36:06.796 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:36:06.798 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:470 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:36:06.798 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:477 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:36:06.799 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:484 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:36:06.799 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:491 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:36:06.799 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:497 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:36:06.799 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:36:06.799 | db426ba8e46b4168ab37351286bf9a32 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:592 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:36:06.800 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:616 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 09:36:06.801 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:631 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:36:06.801 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:636 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 09:36:06.801 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:637 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:36:06.801 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:641 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:36:08.173 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:674 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:36:10.649 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:686 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:36:10.649 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:687 -    - 设备: cuda
2025-07-28 09:36:10.649 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:688 -    - 图像尺寸: 640
2025-07-28 09:36:10.649 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 -    - 置信度阈值: 0.25
2025-07-28 09:36:10.649 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:690 -    - NMS阈值: 0.45
2025-07-28 09:36:10.651 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:703 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:36:10.668 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3226 - 任务 12 的监控流已启动
2025-07-28 09:36:10.670 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:36:10.670 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:751 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:36:10.671 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:781 - 成功预导入YOLOv5 utils模块
2025-07-28 09:36:10.671 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:787 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:36:10.672 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: zql_detect
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:803 - 重新加载模块: model
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 成功导入智驱力模型
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['confidence_threshold', 'conf_thres']，使用默认值: 0.25
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['nms_threshold', 'nms_thres']，使用默认值: 0.45
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:548 - 未找到配置参数 ['input_size', 'img_size']，使用默认值: 640
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:36:10.673 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:819 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:36:10.810 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:832 - 智驱力模型初始化成功
2025-07-28 09:36:10.810 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:833 -    - 设备: cuda
2025-07-28 09:36:10.810 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:834 -    - 图像尺寸: 640
2025-07-28 09:36:10.810 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:835 -    - 置信度阈值: 0.25
2025-07-28 09:36:10.810 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:836 -    - NMS阈值: 0.45
2025-07-28 09:36:10.811 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:detection_loop:849 - 智驱力后处理器初始化成功
2025-07-28 09:38:21.345 | db426ba8e46b4168ab37351286bf9a32 | ERROR    | module_stream.service.task_execution_service:detection_loop:863 - 无法打开视频流: rtsp://127.0.0.1:8554/test1
2025-07-28 09:38:21.369 | d2ff889ad0054876b05993ec08094c1b | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-28 09:38:21.370 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:38:21.370 | db426ba8e46b4168ab37351286bf9a32 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:38:21.375 | 948f558e7acd4fe095e44b1975ecb84d | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-28 09:38:21.396 | 8edeb9cafa414d8b9a2ffdeb51e029ec | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-28 09:38:21.429 | 5038cadb70974403986a9fb34c662d61 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-28 09:38:21.433 | 9a44eb3caadd4e05a843a06d49af40ec | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-28 09:38:21.479 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 09:38:21.479 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
